using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace WpfApp1;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
	public MainWindow()
	{
		InitializeComponent();
		this.Loaded += MainWindow_Loaded;
	}

	private void MainWindow_Loaded(object sender, RoutedEventArgs e)
	{
		SetWindowToSecondaryScreen();
	}

	private void SetWindowToSecondaryScreen()
	{
		try
		{
			// 显示屏幕信息用于调试
			string debugInfo = MultiScreenManager.GetScreensDescription();
			MessageBox.Show(debugInfo, "屏幕信息");

			// 检查是否有多个屏幕
			if (MultiScreenManager.HasMultipleScreens())
			{
				// 尝试移动到副屏
				bool success = MultiScreenManager.MoveWindowToSecondaryScreen(this, useWorkArea: false);

				if (success)
				{
					var secondaryScreen = MultiScreenManager.GetSecondaryScreen();
					if (secondaryScreen != null)
					{
						MessageBox.Show($"检测到副屏在{secondaryScreen.Position}\n" +
									   $"窗口已移动到副屏\n" +
									   $"位置: ({secondaryScreen.Left}, {secondaryScreen.Top})\n" +
									   $"大小: {secondaryScreen.Width} x {secondaryScreen.Height}",
									   "副屏显示");
					}
				}
				else
				{
					MessageBox.Show("未找到副屏，保持在主屏显示", "未找到副屏");
				}
			}
			else
			{
				MessageBox.Show("只检测到一个屏幕，保持在主屏显示", "单屏显示");
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show($"设置副屏显示时出错: {ex.Message}", "错误");
		}
	}

	private void Button_Click(object sender, RoutedEventArgs e)
	{
		MessageBox.Show("透明按钮被点击了！");
	}


}