using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace WpfApp1;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
	public MainWindow()
	{
		InitializeComponent();
		this.Loaded += MainWindow_Loaded;
	}

	private void MainWindow_Loaded(object sender, RoutedEventArgs e)
	{
		SetWindowToSecondaryScreen();
	}

	private void SetWindowToSecondaryScreen()
	{
		try
		{
			// 获取屏幕信息
			double virtualScreenWidth = SystemParameters.VirtualScreenWidth;
			double virtualScreenLeft = SystemParameters.VirtualScreenLeft;
			double virtualScreenTop = SystemParameters.VirtualScreenTop;
			double virtualScreenHeight = SystemParameters.VirtualScreenHeight;
			double primaryScreenWidth = SystemParameters.PrimaryScreenWidth;
			double primaryScreenHeight = SystemParameters.PrimaryScreenHeight;

			// 显示详细的屏幕信息用于调试
			string debugInfo = $"屏幕信息详情:\n" +
							  $"虚拟屏幕宽度: {virtualScreenWidth}\n" +
							  $"虚拟屏幕高度: {virtualScreenHeight}\n" +
							  $"虚拟屏幕左边界: {virtualScreenLeft}\n" +
							  $"虚拟屏幕顶边界: {virtualScreenTop}\n" +
							  $"主屏幕宽度: {primaryScreenWidth}\n" +
							  $"主屏幕高度: {primaryScreenHeight}\n" +
							  $"计算的副屏宽度: {virtualScreenWidth - primaryScreenWidth}";

			MessageBox.Show(debugInfo, "屏幕参数调试");

			// 如果虚拟屏幕宽度大于主屏幕宽度，说明有多个屏幕
			if (virtualScreenWidth > primaryScreenWidth)
			{
				// 先设置窗口状态为Normal，这样才能设置位置和大小
				this.WindowState = WindowState.Normal;

				double secondaryScreenLeft;
				double secondaryScreenWidth;
				double secondaryScreenTop = virtualScreenTop;
				double secondaryScreenHeight = virtualScreenHeight;
				string screenPosition;

				// 根据您的实际情况：虚拟屏幕左边界=0，说明副屏在右侧
				// 但是由于显示缩放，我们需要使用实际的物理尺寸
				if (virtualScreenLeft < 0)
				{
					// 副屏在左侧，主屏在右侧
					secondaryScreenLeft = virtualScreenLeft;
					secondaryScreenWidth = virtualScreenWidth - primaryScreenWidth;
					screenPosition = "左侧";
				}
				else
				{
					// 主屏在左侧，副屏在右侧（您的当前配置）
					secondaryScreenLeft = primaryScreenWidth; // 副屏从主屏右边开始
					secondaryScreenWidth = virtualScreenWidth - primaryScreenWidth; // 1536
					screenPosition = "右侧";

					// 由于显示缩放问题，强制设置为实际的1920宽度
					// 如果计算出的宽度不是1920，则使用1920
					// if (Math.Abs(secondaryScreenWidth - 1920) > 100) // 允许一些误差
					// {
					// 	secondaryScreenWidth = 1920; // 强制使用实际的副屏宽度
					// 	MessageBox.Show($"检测到显示缩放，副屏宽度从 {virtualScreenWidth - primaryScreenWidth} 调整为 1920", "缩放调整");
					// }
				}

				// 设置窗口位置和大小到副屏
				this.Left = secondaryScreenLeft;
				this.Top = secondaryScreenTop;
				this.Width = secondaryScreenWidth;
				this.Height = secondaryScreenHeight;

				// 确保窗口完全在副屏上显示
				this.WindowStartupLocation = WindowStartupLocation.Manual;

				MessageBox.Show($"检测到副屏在{screenPosition}\n窗口已移动到副屏\n位置: ({secondaryScreenLeft}, {secondaryScreenTop})\n大小: {secondaryScreenWidth} x {secondaryScreenHeight}", "副屏显示");
			}
			else
			{
				MessageBox.Show("只检测到一个屏幕，保持在主屏显示", "单屏显示");
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show($"设置副屏显示时出错: {ex.Message}", "错误");
		}
	}

	private void Button_Click(object sender, RoutedEventArgs e)
	{
		MessageBox.Show("透明按钮被点击了！");
	}


}