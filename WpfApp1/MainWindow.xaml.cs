using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace WpfApp1;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
	public MainWindow()
	{
		InitializeComponent();
		SetWindowToSecondaryScreen();
	}

	private void SetWindowToSecondaryScreen()
	{
		// 使用WPF的SystemParameters来检测多屏幕
		double virtualScreenWidth = SystemParameters.VirtualScreenWidth;
		double virtualScreenLeft = SystemParameters.VirtualScreenLeft;
		double primaryScreenWidth = SystemParameters.PrimaryScreenWidth;

		// 如果虚拟屏幕宽度大于主屏幕宽度，说明有多个屏幕
		if (virtualScreenWidth > primaryScreenWidth)
		{
			// 计算副屏的位置
			double secondaryScreenLeft = primaryScreenWidth;

			// 如果虚拟屏幕的左边界小于0，说明副屏在左边
			if (virtualScreenLeft < 0)
			{
				secondaryScreenLeft = virtualScreenLeft;
			}

			// 设置窗口位置到副屏
			this.Left = secondaryScreenLeft;
			this.Top = 0;

			// 设置窗口大小为副屏大小（假设副屏和主屏高度相同）
			this.Width = virtualScreenWidth - primaryScreenWidth;
			this.Height = SystemParameters.PrimaryScreenHeight;
		}
		// 如果只有一个屏幕，保持在主屏上（默认行为）
	}

	private void Button_Click(object sender, RoutedEventArgs e)
	{
		MessageBox.Show("透明按钮被点击了！");
	}


}