using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace WpfApp1;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
	public MainWindow()
	{
		InitializeComponent();
		this.Loaded += MainWindow_Loaded;
	}

	private void MainWindow_Loaded(object sender, RoutedEventArgs e)
	{
		SetWindowToSecondaryScreen();
	}

	private void SetWindowToSecondaryScreen()
	{
		try
		{
			// 获取屏幕信息
			double virtualScreenWidth = SystemParameters.VirtualScreenWidth;
			double virtualScreenLeft = SystemParameters.VirtualScreenLeft;
			double virtualScreenTop = SystemParameters.VirtualScreenTop;
			double virtualScreenHeight = SystemParameters.VirtualScreenHeight;
			double primaryScreenWidth = SystemParameters.PrimaryScreenWidth;

			// 如果虚拟屏幕宽度大于主屏幕宽度，说明有多个屏幕
			if (virtualScreenWidth > primaryScreenWidth)
			{
				// 先设置窗口状态为Normal，这样才能设置位置和大小
				this.WindowState = WindowState.Normal;

				// 根据您的配置：副屏在左边，宽度1920，位置从-1920开始
				double secondaryScreenLeft = virtualScreenLeft; // -1920
				double secondaryScreenWidth = Math.Abs(virtualScreenLeft); // 1920
				double secondaryScreenTop = virtualScreenTop; // 通常是0
				double secondaryScreenHeight = virtualScreenHeight; // 使用虚拟屏幕的完整高度

				// 设置窗口位置和大小到副屏
				this.Left = secondaryScreenLeft;
				this.Top = secondaryScreenTop;
				this.Width = secondaryScreenWidth;
				this.Height = secondaryScreenHeight;

				// 确保窗口完全在副屏上显示
				this.WindowStartupLocation = WindowStartupLocation.Manual;

				MessageBox.Show($"窗口已移动到副屏\n位置: ({secondaryScreenLeft}, {secondaryScreenTop})\n大小: {secondaryScreenWidth} x {secondaryScreenHeight}", "副屏显示");
			}
			else
			{
				MessageBox.Show("只检测到一个屏幕，保持在主屏显示", "单屏显示");
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show($"设置副屏显示时出错: {ex.Message}", "错误");
		}
	}

	private void Button_Click(object sender, RoutedEventArgs e)
	{
		MessageBox.Show("透明按钮被点击了！");
	}


}