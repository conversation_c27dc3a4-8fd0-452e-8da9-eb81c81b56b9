using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace WpfApp1;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
	public MainWindow()
	{
		InitializeComponent();
		SetWindowToSecondaryScreen();
	}

	private void SetWindowToSecondaryScreen()
	{
		// 获取所有屏幕
		Screen[] screens = Screen.AllScreens;

		// 如果有多个屏幕，使用副屏（非主屏）
		if (screens.Length > 1)
		{
			Screen? secondaryScreen = null;

			// 查找第一个非主屏幕
			foreach (Screen screen in screens)
			{
				if (!screen.Primary)
				{
					secondaryScreen = screen;
					break;
				}
			}

			// 如果找到副屏，将窗口移动到副屏
			if (secondaryScreen != null)
			{
				window.WindowStartupLocation = WindowStartupLocation.Manual;
				// 设置窗口位置到副屏的左上角
				this.Left = secondaryScreen.WorkingArea.Left;
				this.Top = secondaryScreen.WorkingArea.Top;

				// 设置窗口大小为副屏的工作区域大小
				this.Width = secondaryScreen.WorkingArea.Width;
				this.Height = secondaryScreen.WorkingArea.Height;
			}
		}
		// 如果只有一个屏幕，保持在主屏上（默认行为）
	}


}