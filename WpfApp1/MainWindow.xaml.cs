using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace WpfApp1;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
	public MainWindow()
	{
		InitializeComponent();
		this.Loaded += MainWindow_Loaded;
	}

	private void MainWindow_Loaded(object sender, RoutedEventArgs e)
	{
		SetWindowToSecondaryScreen();
	}

	private void SetWindowToSecondaryScreen()
	{
		try
		{
			// 获取屏幕信息
			double virtualScreenWidth = SystemParameters.VirtualScreenWidth;
			double virtualScreenLeft = SystemParameters.VirtualScreenLeft;
			double primaryScreenWidth = SystemParameters.PrimaryScreenWidth;
			double primaryScreenHeight = SystemParameters.PrimaryScreenHeight;

			// 显示调试信息
			string debugInfo = $"虚拟屏幕宽度: {virtualScreenWidth}\n" +
							  $"虚拟屏幕左边界: {virtualScreenLeft}\n" +
							  $"主屏幕宽度: {primaryScreenWidth}\n" +
							  $"主屏幕高度: {primaryScreenHeight}";

			MessageBox.Show(debugInfo, "屏幕信息");

			// 如果虚拟屏幕宽度大于主屏幕宽度，说明有多个屏幕
			if (virtualScreenWidth > primaryScreenWidth)
			{
				// 先设置窗口状态为Normal，这样才能设置位置
				this.WindowState = WindowState.Normal;

				// 计算副屏的位置
				double secondaryScreenLeft;
				double secondaryScreenWidth = virtualScreenWidth - primaryScreenWidth;

				// 如果虚拟屏幕的左边界小于0，说明副屏在左边
				if (virtualScreenLeft < 0)
				{
					secondaryScreenLeft = virtualScreenLeft;
				}
				else
				{
					// 副屏在右边
					secondaryScreenLeft = primaryScreenWidth;
				}

				// 设置窗口位置和大小
				this.Left = secondaryScreenLeft;
				this.Top = 0;
				this.Width = secondaryScreenWidth;
				this.Height = primaryScreenHeight;

				// 重新设置为最大化状态
				this.WindowState = WindowState.Maximized;

				MessageBox.Show($"窗口已移动到副屏\n位置: ({secondaryScreenLeft}, 0)\n大小: {secondaryScreenWidth} x {primaryScreenHeight}", "副屏显示");
			}
			else
			{
				MessageBox.Show("只检测到一个屏幕，保持在主屏显示", "单屏显示");
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show($"设置副屏显示时出错: {ex.Message}", "错误");
		}
	}

	private void Button_Click(object sender, RoutedEventArgs e)
	{
		MessageBox.Show("透明按钮被点击了！");
	}


}