using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Runtime.InteropServices;

namespace WpfApp1;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
	// P/Invoke declarations for multi-monitor support
	[DllImport("user32.dll")]
	private static extern bool EnumDisplayMonitors(IntPtr hdc, IntPtr lprcClip, MonitorEnumProc lpfnEnum, IntPtr dwData);

	[DllImport("user32.dll")]
	private static extern bool GetMonitorInfo(IntPtr hmon, ref MONITORINFO lpmi);

	private delegate bool MonitorEnumProc(IntPtr hMonitor, IntPtr hdcMonitor, ref RECT lprcMonitor, IntPtr dwData);

	[StructLayout(LayoutKind.Sequential)]
	private struct RECT
	{
		public int Left;
		public int Top;
		public int Right;
		public int Bottom;
	}

	[StructLayout(LayoutKind.Sequential)]
	private struct MONITORINFO
	{
		public int cbSize;
		public RECT rcMonitor;
		public RECT rcWork;
		public uint dwFlags;
	}

	private const uint MONITORINFOF_PRIMARY = 1;

	public MainWindow()
	{
		InitializeComponent();
		this.Loaded += MainWindow_Loaded;
	}

	private void MainWindow_Loaded(object sender, RoutedEventArgs e)
	{
		SetWindowToSecondaryScreen();
	}

	private void SetWindowToSecondaryScreen()
	{
		try
		{
			var monitors = new List<MONITORINFO>();

			// 枚举所有显示器
			EnumDisplayMonitors(IntPtr.Zero, IntPtr.Zero, (IntPtr hMonitor, IntPtr hdcMonitor, ref RECT lprcMonitor, IntPtr dwData) =>
			{
				MONITORINFO mi = new MONITORINFO();
				mi.cbSize = Marshal.SizeOf(mi);
				if (GetMonitorInfo(hMonitor, ref mi))
				{
					monitors.Add(mi);
				}
				return true;
			}, IntPtr.Zero);

			// 显示屏幕信息用于调试
			string debugInfo = $"检测到 {monitors.Count} 个屏幕:\n\n";
			for (int i = 0; i < monitors.Count; i++)
			{
				var monitor = monitors[i];
				bool isPrimary = (monitor.dwFlags & MONITORINFOF_PRIMARY) != 0;
				debugInfo += $"屏幕 {i + 1}:\n";
				debugInfo += $"  主屏幕: {isPrimary}\n";
				debugInfo += $"  边界: {monitor.rcMonitor.Left}, {monitor.rcMonitor.Top}, {monitor.rcMonitor.Right - monitor.rcMonitor.Left}x{monitor.rcMonitor.Bottom - monitor.rcMonitor.Top}\n";
				debugInfo += $"  工作区: {monitor.rcWork.Left}, {monitor.rcWork.Top}, {monitor.rcWork.Right - monitor.rcWork.Left}x{monitor.rcWork.Bottom - monitor.rcWork.Top}\n\n";
			}

			MessageBox.Show(debugInfo, "屏幕信息");

			// 如果有多个屏幕，查找副屏
			if (monitors.Count > 1)
			{
				MONITORINFO? secondaryMonitor = null;

				// 查找第一个非主屏幕
				foreach (var monitor in monitors)
				{
					if ((monitor.dwFlags & MONITORINFOF_PRIMARY) == 0)
					{
						secondaryMonitor = monitor;
						break;
					}
				}

				if (secondaryMonitor.HasValue)
				{
					var secondary = secondaryMonitor.Value;

					// 先设置窗口状态为Normal，这样才能设置位置和大小
					this.WindowState = WindowState.Normal;

					// 使用副屏的边界信息
					this.Left = secondary.rcMonitor.Left;
					this.Top = secondary.rcMonitor.Top;
					this.Width = secondary.rcMonitor.Right - secondary.rcMonitor.Left;
					this.Height = secondary.rcMonitor.Bottom - secondary.rcMonitor.Top;

					// 确保窗口完全在副屏上显示
					this.WindowStartupLocation = WindowStartupLocation.Manual;

					string position = secondary.rcMonitor.Left < 0 ? "左侧" : "右侧";
					MessageBox.Show($"检测到副屏在{position}\n" +
								   $"窗口已移动到副屏\n" +
								   $"位置: ({secondary.rcMonitor.Left}, {secondary.rcMonitor.Top})\n" +
								   $"大小: {secondary.rcMonitor.Right - secondary.rcMonitor.Left} x {secondary.rcMonitor.Bottom - secondary.rcMonitor.Top}",
								   "副屏显示");
				}
				else
				{
					MessageBox.Show("未找到副屏，保持在主屏显示", "未找到副屏");
				}
			}
			else
			{
				MessageBox.Show("只检测到一个屏幕，保持在主屏显示", "单屏显示");
			}
		}
		catch (Exception ex)
		{
			MessageBox.Show($"设置副屏显示时出错: {ex.Message}", "错误");
		}
	}

	private void Button_Click(object sender, RoutedEventArgs e)
	{
		MessageBox.Show("透明按钮被点击了！");
	}


}