
<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net8.0-windows</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UseWPF>true</UseWPF>
        <SatelliteResourceLanguages>en</SatelliteResourceLanguages>
        <NeutralLanguage>en</NeutralLanguage>
        <AssemblyNeutralResourcesLanguage>en</AssemblyNeutralResourcesLanguage>
    </PropertyGroup>

    <ItemGroup>
        <Content Include="Images\**\*">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </Content>
    </ItemGroup>

    <ItemGroup>
      <Page Update="ButtonStyle.xaml">
        <Generator>MSBuild:Compile</Generator>
        <XamlRuntime>Wpf</XamlRuntime>
        <SubType>Designer</SubType>
      </Page>
    </ItemGroup>
    
</Project>
