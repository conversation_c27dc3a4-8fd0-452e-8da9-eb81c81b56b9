using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.InteropServices;
using System.Windows;

namespace WpfApp1
{
    /// <summary>
    /// 多屏幕管理器，用于检测和管理多屏幕显示
    /// </summary>
    public class MultiScreenManager
    {
        #region P/Invoke Declarations

        [DllImport("user32.dll")]
        private static extern bool EnumDisplayMonitors(IntPtr hdc, IntPtr lprcClip, MonitorEnumProc lpfnEnum, IntPtr dwData);

        [DllImport("user32.dll")]
        private static extern bool GetMonitorInfo(IntPtr hmon, ref MONITORINFO lpmi);

        private delegate bool MonitorEnumProc(IntPtr hMonitor, IntPtr hdcMonitor, ref RECT lprcMonitor, IntPtr dwData);

        [StructLayout(LayoutKind.Sequential)]
        private struct RECT
        {
            public int Left;
            public int Top;
            public int Right;
            public int Bottom;
        }

        [StructLayout(LayoutKind.Sequential)]
        private struct MONITORINFO
        {
            public int cbSize;
            public RECT rcMonitor;
            public RECT rcWork;
            public uint dwFlags;
        }

        private const uint MONITORINFOF_PRIMARY = 1;

        #endregion

        #region Public Classes

        /// <summary>
        /// 屏幕信息类
        /// </summary>
        public class ScreenInfo
        {
            public bool IsPrimary { get; set; }
            public int Left { get; set; }
            public int Top { get; set; }
            public int Width { get; set; }
            public int Height { get; set; }
            public int WorkAreaLeft { get; set; }
            public int WorkAreaTop { get; set; }
            public int WorkAreaWidth { get; set; }
            public int WorkAreaHeight { get; set; }

            public string Position => Left < 0 ? "左侧" : "右侧";

            public override string ToString()
            {
                return $"屏幕 - 主屏幕: {IsPrimary}, 位置: {Position}, " +
                       $"边界: ({Left}, {Top}, {Width}x{Height}), " +
                       $"工作区: ({WorkAreaLeft}, {WorkAreaTop}, {WorkAreaWidth}x{WorkAreaHeight})";
            }
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// 获取所有屏幕信息
        /// </summary>
        /// <returns>屏幕信息列表</returns>
        public static List<ScreenInfo> GetAllScreens()
        {
            var screens = new List<ScreenInfo>();
            var monitors = new List<MONITORINFO>();

            // 枚举所有显示器
            EnumDisplayMonitors(IntPtr.Zero, IntPtr.Zero, (IntPtr hMonitor, IntPtr hdcMonitor, ref RECT lprcMonitor, IntPtr dwData) =>
            {
                MONITORINFO mi = new MONITORINFO();
                mi.cbSize = Marshal.SizeOf(mi);
                if (GetMonitorInfo(hMonitor, ref mi))
                {
                    monitors.Add(mi);
                }
                return true;
            }, IntPtr.Zero);

            // 转换为ScreenInfo对象
            foreach (var monitor in monitors)
            {
                screens.Add(new ScreenInfo
                {
                    IsPrimary = (monitor.dwFlags & MONITORINFOF_PRIMARY) != 0,
                    Left = monitor.rcMonitor.Left,
                    Top = monitor.rcMonitor.Top,
                    Width = monitor.rcMonitor.Right - monitor.rcMonitor.Left,
                    Height = monitor.rcMonitor.Bottom - monitor.rcMonitor.Top,
                    WorkAreaLeft = monitor.rcWork.Left,
                    WorkAreaTop = monitor.rcWork.Top,
                    WorkAreaWidth = monitor.rcWork.Right - monitor.rcWork.Left,
                    WorkAreaHeight = monitor.rcWork.Bottom - monitor.rcWork.Top
                });
            }

            return screens;
        }

        /// <summary>
        /// 获取主屏幕信息
        /// </summary>
        /// <returns>主屏幕信息，如果未找到返回null</returns>
        public static ScreenInfo? GetPrimaryScreen()
        {
            var screens = GetAllScreens();
            return screens.FirstOrDefault(s => s.IsPrimary);
        }

        /// <summary>
        /// 获取副屏幕信息
        /// </summary>
        /// <returns>副屏幕信息，如果未找到返回null</returns>
        public static ScreenInfo? GetSecondaryScreen()
        {
            var screens = GetAllScreens();
            return screens.FirstOrDefault(s => !s.IsPrimary);
        }

        /// <summary>
        /// 检查是否有多个屏幕
        /// </summary>
        /// <returns>如果有多个屏幕返回true</returns>
        public static bool HasMultipleScreens()
        {
            return GetAllScreens().Count > 1;
        }

        /// <summary>
        /// 将窗口移动到指定屏幕
        /// </summary>
        /// <param name="window">要移动的窗口</param>
        /// <param name="screen">目标屏幕</param>
        /// <param name="useWorkArea">是否使用工作区域（排除任务栏）</param>
        public static void MoveWindowToScreen(Window window, ScreenInfo screen, bool useWorkArea = false)
        {
            if (window == null || screen == null) return;

            // 先设置窗口状态为Normal，这样才能设置位置和大小
            window.WindowState = WindowState.Normal;

            if (useWorkArea)
            {
                window.Left = screen.WorkAreaLeft;
                window.Top = screen.WorkAreaTop;
                window.Width = screen.WorkAreaWidth;
                window.Height = screen.WorkAreaHeight;
            }
            else
            {
                window.Left = screen.Left;
                window.Top = screen.Top;
                window.Width = screen.Width;
                window.Height = screen.Height;
            }

            // 确保窗口完全在指定屏幕上显示
            window.WindowStartupLocation = WindowStartupLocation.Manual;
        }

        /// <summary>
        /// 将窗口移动到副屏幕（如果存在）
        /// </summary>
        /// <param name="window">要移动的窗口</param>
        /// <param name="useWorkArea">是否使用工作区域（排除任务栏）</param>
        /// <returns>如果成功移动到副屏返回true</returns>
        public static bool MoveWindowToSecondaryScreen(Window window, bool useWorkArea = false)
        {
            var secondaryScreen = GetSecondaryScreen();
            if (secondaryScreen != null)
            {
                MoveWindowToScreen(window, secondaryScreen, useWorkArea);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取屏幕信息的详细描述
        /// </summary>
        /// <returns>包含所有屏幕信息的字符串</returns>
        public static string GetScreensDescription()
        {
            var screens = GetAllScreens();
            var description = $"检测到 {screens.Count} 个屏幕:\n\n";

            for (int i = 0; i < screens.Count; i++)
            {
                var screen = screens[i];
                description += $"屏幕 {i + 1}:\n";
                description += $"  主屏幕: {screen.IsPrimary}\n";
                description += $"  位置: {screen.Position}\n";
                description += $"  边界: {screen.Left}, {screen.Top}, {screen.Width}x{screen.Height}\n";
                description += $"  工作区: {screen.WorkAreaLeft}, {screen.WorkAreaTop}, {screen.WorkAreaWidth}x{screen.WorkAreaHeight}\n\n";
            }

            return description;
        }

        #endregion
    }
}
