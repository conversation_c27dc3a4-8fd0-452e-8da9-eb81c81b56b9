<Window
    x:Class="WpfApp1.MainWindow"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:WpfApp1"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    Title="MainWindow"
    Background="White"
    ResizeMode="NoResize"
    WindowState="Maximized"
    WindowStyle="None"
    mc:Ignorable="d">
    <Viewbox Stretch="Uniform">
        <Grid Width="1920" Height="1080">
            <Button
                Width="500"
                Height="500"
                Margin="0,50,50,0"
                HorizontalAlignment="Right"
                VerticalAlignment="Top"
                Style="{StaticResource ImageButtonStyle}">
                <Image Source="/Images/leakage.png" Stretch="Uniform" />
            </Button>

            <Button
                Width="500"
                Height="500"
                Margin="50,50,0,0"
                HorizontalAlignment="Left"
                VerticalAlignment="Top"
                Style="{StaticResource ImageButtonStyle}">
                <Image Source="/Images/gongyi_weixuanzhong.png" Stretch="Uniform" />
            </Button>

            <Button
                Width="500"
                Height="130"
                Margin="0,0,50,50"
                HorizontalAlignment="Center"
                VerticalAlignment="Bottom"
                Style="{StaticResource ImageButtonStyle}">
                <Image Source="/Images/Path1134.png" />
            </Button>
        </Grid>
    </Viewbox>
</Window>
