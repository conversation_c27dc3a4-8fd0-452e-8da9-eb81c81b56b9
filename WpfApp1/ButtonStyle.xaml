<Window x:Class="WpfApp1.ButtonStyle"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfApp1"
        mc:Ignorable="d"
        Title="ButtonStyle" Height="450" Width="800" Background="White">
    <Grid>
        <Button Style="{StaticResource ImageButtonStyle}"
                Width="100" Height="100"
                Margin="100,10,0,0">
            <Image Source="/Images/leakage.png" Stretch="Uniform"/>
        </Button>
    </Grid>
</Window>
